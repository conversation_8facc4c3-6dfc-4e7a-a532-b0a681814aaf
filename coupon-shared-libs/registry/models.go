package registry

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// ServiceCredential represents a service's authentication credentials in the registry
type ServiceCredential struct {
	ID            string    `gorm:"type:uuid;primary_key" json:"id"`
	Name          string    `gorm:"type:varchar(255);not null;unique" json:"name"`
	ClientID      string    `gorm:"type:varchar(255);not null;unique" json:"client_id"`
	ClientKeyHash string    `gorm:"not null" json:"-"` // Never expose the hash
	Version       string    `gorm:"type:varchar(50)" json:"version"`
	CreatedAt     time.Time `gorm:"not null" json:"created_at"`
	UpdatedAt     time.Time `gorm:"not null" json:"updated_at"`
}

// TableName returns the table name for ServiceCredential
func (ServiceCredential) TableName() string {
	return "service_credentials"
}

// NewServiceCredential creates a new service credential with generated client ID and key
func NewServiceCredential(name, version string) (*ServiceCredential, string, error) {
	rawKey, err := generateRandomKey(32)
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate random key: %w", err)
	}

	keyHash, err := hashKey(rawKey)
	if err != nil {
		return nil, "", fmt.Errorf("failed to hash key: %w", err)
	}

	return &ServiceCredential{
		ID:            uuid.NewString(),
		Name:          name,
		Version:       version,
		ClientID:      uuid.NewString(),
		ClientKeyHash: keyHash,
	}, rawKey, nil
}

// ValidateKey checks if the provided key matches the stored hash
func (s *ServiceCredential) ValidateKey(key string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(s.ClientKeyHash), []byte(key))
	return err == nil
}

// generateRandomKey generates a random key of the specified length
func generateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// hashKey hashes a key using bcrypt
func hashKey(key string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(key), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}
