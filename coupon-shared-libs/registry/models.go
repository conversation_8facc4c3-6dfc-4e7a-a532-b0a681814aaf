package registry

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ServiceInstance represents a registered service instance in the registry
type ServiceInstance struct {
	ID            string    `gorm:"primaryKey;type:varchar(255)" json:"id"`
	ServiceName   string    `gorm:"type:varchar(100);not null;index" json:"service_name"`
	Host          string    `gorm:"type:varchar(255);not null" json:"host"`
	GRPCPort      int       `gorm:"not null" json:"grpc_port"`
	Status        string    `gorm:"type:varchar(20);not null;index;default:'HEALTHY'" json:"status"`
	LastHeartbeat time.Time `gorm:"not null" json:"last_heartbeat"`
	RegisteredAt  time.Time `gorm:"not null" json:"registered_at"`

	// GORM timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ServiceStatus constants
const (
	ServiceStatusHealthy      = "HEALTHY"
	ServiceStatusUnhealthy    = "UNHEALTHY"
	ServiceStatusDeregistered = "DEREGISTERED"
)

// TableName returns the table name for ServiceInstance
func (ServiceInstance) TableName() string {
	return "service_instances"
}

// IsHealthy returns true if the service instance is healthy
func (s *ServiceInstance) IsHealthy() bool {
	return s.Status == ServiceStatusHealthy
}

// IsExpired returns true if the service instance hasn't sent heartbeat within the given duration
func (s *ServiceInstance) IsExpired(ttl time.Duration) bool {
	return time.Since(s.LastHeartbeat) > ttl
}

// GetGRPCAddress returns the gRPC address for the service instance
func (s *ServiceInstance) GetGRPCAddress() string {
	return fmt.Sprintf("%s:%d", s.Host, s.GRPCPort)
}
