package registry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/grpc/health/grpc_health_v1"
)

// HealthReporter integrates with service registry to report and monitor health status
type HealthReporter struct {
	registrar  *ServiceRegistrar
	repository Repository
	logger     *logging.Logger

	// Health checks
	checks      map[string]HealthCheckFunc
	checksMutex sync.RWMutex

	// Lifecycle
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
	interval time.Duration
}

// HealthCheckFunc defines a health check function that returns an error if unhealthy
type HealthCheckFunc func(ctx context.Context) error

// NewHealthReporter creates a new health reporter for monitoring service health
func NewHealthReporter(registrar *ServiceRegistrar, repo Repository, logger *logging.Logger) *HealthReporter {
	ctx, cancel := context.WithCancel(context.Background())

	return &HealthReporter{
		registrar:  registrar,
		repository: repo,
		logger:     logger,
		checks:     make(map[string]HealthCheckFunc),
		ctx:        ctx,
		cancel:     cancel,
		interval:   30 * time.Second, // Default health check interval
	}
}

// AddHealthCheck adds a health check function
func (h *HealthReporter) AddHealthCheck(name string, checkFunc HealthCheckFunc) {
	h.checksMutex.Lock()
	defer h.checksMutex.Unlock()

	h.checks[name] = checkFunc

	h.logger.WithFields(logging.Fields{
		"check_name": name,
	}).Info("Health check added")
}

// RemoveHealthCheck removes a health check function
func (h *HealthReporter) RemoveHealthCheck(name string) {
	h.checksMutex.Lock()
	defer h.checksMutex.Unlock()

	delete(h.checks, name)

	h.logger.WithFields(logging.Fields{
		"check_name": name,
	}).Info("Health check removed")
}

// Start begins the health reporting loop
func (h *HealthReporter) Start() {
	h.wg.Add(1)
	go h.healthCheckLoop()

	h.logger.Info("Health reporter started")
}

// Stop stops the health reporting loop
func (h *HealthReporter) Stop() {
	h.cancel()
	h.wg.Wait()

	h.logger.Info("Health reporter stopped")
}

// SetInterval sets the health check interval
func (h *HealthReporter) SetInterval(interval time.Duration) {
	h.interval = interval
}

// RunHealthChecks runs all health checks and reports results
func (h *HealthReporter) RunHealthChecks(ctx context.Context) error {
	h.checksMutex.RLock()
	checks := make(map[string]HealthCheckFunc)
	for name, checkFunc := range h.checks {
		checks[name] = checkFunc
	}
	h.checksMutex.RUnlock()

	if len(checks) == 0 {
		// No health checks configured, assume healthy
		return h.updateServiceHealth(ctx, true)
	}

	overallHealthy := true

	// Run each health check
	for checkName, checkFunc := range checks {
		start := time.Now()
		err := checkFunc(ctx)
		duration := time.Since(start)

		if err != nil {
			overallHealthy = false
			h.logger.WithFields(logging.Fields{
				"check_name": checkName,
				"error":      err.Error(),
				"duration":   duration.String(),
			}).Error("Health check failed")
		} else {
			h.logger.WithFields(logging.Fields{
				"check_name": checkName,
				"duration":   duration.String(),
			}).Debug("Health check passed")
		}
	}

	return h.updateServiceHealth(ctx, overallHealthy)
}

// updateServiceHealth updates the service health status
func (h *HealthReporter) updateServiceHealth(ctx context.Context, isHealthy bool) error {
	// Update registrar status
	if isHealthy {
		if err := h.registrar.MarkHealthy(ctx); err != nil {
			h.logger.WithFields(logging.Fields{
				"error": err.Error(),
			}).Error("Failed to mark service as healthy")
		}
	} else {
		if err := h.registrar.MarkUnhealthy(ctx); err != nil {
			h.logger.WithFields(logging.Fields{
				"error": err.Error(),
			}).Error("Failed to mark service as unhealthy")
		}
	}

	return nil
}

// healthCheckLoop runs health checks periodically
func (h *HealthReporter) healthCheckLoop() {
	defer h.wg.Done()

	ticker := time.NewTicker(h.interval)
	defer ticker.Stop()

	// Run initial health check
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	if err := h.RunHealthChecks(ctx); err != nil {
		h.logger.WithError(err).Error("Initial health check failed")
	}
	cancel()

	// Periodic health checks
	for {
		select {
		case <-h.ctx.Done():
			return
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			if err := h.RunHealthChecks(ctx); err != nil {
				h.logger.WithError(err).Error("Health check failed")
			}
			cancel()
		}
	}
}

// GRPCHealthServer implements the gRPC health checking protocol
type GRPCHealthServer struct {
	grpc_health_v1.UnimplementedHealthServer
	healthReporter *HealthReporter
	logger         *logging.Logger
}

// NewGRPCHealthServer creates a new gRPC health server
func NewGRPCHealthServer(healthReporter *HealthReporter, logger *logging.Logger) *GRPCHealthServer {
	return &GRPCHealthServer{
		healthReporter: healthReporter,
		logger:         logger,
	}
}

// Check implements the gRPC health check method
func (s *GRPCHealthServer) Check(ctx context.Context, req *grpc_health_v1.HealthCheckRequest) (*grpc_health_v1.HealthCheckResponse, error) {
	// Run health checks
	if err := s.healthReporter.RunHealthChecks(ctx); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Health check failed")
		return &grpc_health_v1.HealthCheckResponse{
			Status: grpc_health_v1.HealthCheckResponse_NOT_SERVING,
		}, nil
	}

	// Check if service is registered and healthy
	if !s.healthReporter.registrar.IsRegistered() {
		return &grpc_health_v1.HealthCheckResponse{
			Status: grpc_health_v1.HealthCheckResponse_NOT_SERVING,
		}, nil
	}

	instance := s.healthReporter.registrar.GetInstance()
	if instance == nil || !instance.IsHealthy() {
		return &grpc_health_v1.HealthCheckResponse{
			Status: grpc_health_v1.HealthCheckResponse_NOT_SERVING,
		}, nil
	}

	return &grpc_health_v1.HealthCheckResponse{
		Status: grpc_health_v1.HealthCheckResponse_SERVING,
	}, nil
}

// Watch implements the gRPC health check streaming method
func (s *GRPCHealthServer) Watch(req *grpc_health_v1.HealthCheckRequest, stream grpc_health_v1.Health_WatchServer) error {
	// For simplicity, we'll send periodic updates
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	// Send initial status
	resp, err := s.Check(stream.Context(), req)
	if err != nil {
		return err
	}

	if err := stream.Send(resp); err != nil {
		return err
	}

	// Send periodic updates
	for {
		select {
		case <-stream.Context().Done():
			return stream.Context().Err()
		case <-ticker.C:
			resp, err := s.Check(stream.Context(), req)
			if err != nil {
				return err
			}

			if err := stream.Send(resp); err != nil {
				return err
			}
		}
	}
}

// HealthCheckRegistry provides a registry of health check functions
type HealthCheckRegistry struct {
	checks map[string]HealthCheckFunc
	mutex  sync.RWMutex
}

// NewHealthCheckRegistry creates a new health check registry
func NewHealthCheckRegistry() *HealthCheckRegistry {
	return &HealthCheckRegistry{
		checks: make(map[string]HealthCheckFunc),
	}
}

// Register registers a health check function
func (r *HealthCheckRegistry) Register(name string, checkFunc HealthCheckFunc) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.checks[name] = checkFunc
}

// Unregister removes a health check function
func (r *HealthCheckRegistry) Unregister(name string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	delete(r.checks, name)
}

// GetChecks returns all registered health check functions
func (r *HealthCheckRegistry) GetChecks() map[string]HealthCheckFunc {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	checks := make(map[string]HealthCheckFunc)
	for name, checkFunc := range r.checks {
		checks[name] = checkFunc
	}

	return checks
}

// CreateDatabaseHealthCheck creates a health check for database connectivity
func CreateDatabaseHealthCheck(healthFunc func(ctx context.Context) error) HealthCheckFunc {
	return func(ctx context.Context) error {
		return healthFunc(ctx)
	}
}

// CreateRedisHealthCheck creates a health check for Redis connectivity
func CreateRedisHealthCheck(healthFunc func(ctx context.Context) error) HealthCheckFunc {
	return func(ctx context.Context) error {
		return healthFunc(ctx)
	}
}

// CreateCustomHealthCheck creates a custom health check
func CreateCustomHealthCheck(name string, checkFunc func(ctx context.Context) error) HealthCheckFunc {
	return func(ctx context.Context) error {
		if err := checkFunc(ctx); err != nil {
			return fmt.Errorf("%s health check failed: %w", name, err)
		}
		return nil
	}
}
