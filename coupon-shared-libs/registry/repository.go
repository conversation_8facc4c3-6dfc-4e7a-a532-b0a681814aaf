package registry

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gorm.io/gorm"
)

// Repository defines the interface for service credential operations
type Repository interface {
	// RegisterService creates a new service credential entry
	RegisterService(ctx context.Context, credential *ServiceCredential) error
	// GetServiceByClientID retrieves a service credential by client ID
	GetServiceByClientID(ctx context.Context, clientID string) (*ServiceCredential, error)
	// ValidateCredentials checks if the provided client ID and key are valid
	ValidateCredentials(ctx context.Context, clientID, clientKey string) (*ServiceCredential, error)
}

// repository implements the Repository interface
type repository struct {
	db     *gorm.DB
	logger *logging.Logger
}

// NewRepository creates a new service registry repository with database connection
func NewRepository(db *gorm.DB, logger *logging.Logger) Repository {
	return &repository{
		db:     db,
		logger: logger,
	}
}

// RegisterService creates a new service credential entry
func (r *repository) RegisterService(ctx context.Context, credential *ServiceCredential) error {
	if err := r.db.WithContext(ctx).Create(credential).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"service_name": credential.Name,
			"client_id":    credential.ClientID,
		}).Error("Failed to register service credential")
		return fmt.Errorf("failed to register service credential: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"service_id":   credential.ID,
		"service_name": credential.Name,
		"client_id":    credential.ClientID,
		"version":      credential.Version,
	}).Info("Service credential registered successfully")

	return nil
}

// GetServiceByClientID retrieves a service credential by client ID
func (r *repository) GetServiceByClientID(ctx context.Context, clientID string) (*ServiceCredential, error) {
	var credential ServiceCredential
	if err := r.db.WithContext(ctx).
		Where("client_id = ?", clientID).
		First(&credential).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("service credential not found for client ID: %s", clientID)
		}
		r.logger.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"client_id": clientID,
		}).Error("Failed to get service credential by client ID")
		return nil, fmt.Errorf("failed to get service credential: %w", err)
	}

	return &credential, nil
}

// ValidateCredentials checks if the provided client ID and key are valid
func (r *repository) ValidateCredentials(ctx context.Context, clientID, clientKey string) (*ServiceCredential, error) {
	credential, err := r.GetServiceByClientID(ctx, clientID)
	if err != nil {
		return nil, err
	}

	if !credential.ValidateKey(clientKey) {
		r.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"client_id":    clientID,
			"service_name": credential.Name,
		}).Warn("Invalid client key provided for service")
		return nil, fmt.Errorf("invalid client key for service: %s", credential.Name)
	}

	r.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"client_id":    clientID,
		"service_name": credential.Name,
	}).Debug("Service credentials validated successfully")

	return credential, nil
}
