package registry

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gorm.io/gorm"
)

// Repository defines the interface for service registry database operations
type Repository interface {
	// Essential service operations
	RegisterService(ctx context.Context, instance *ServiceInstance) error
	UpdateHeartbeat(ctx context.Context, serviceID string) error
	DeregisterService(ctx context.Context, serviceID string) error
	GetHealthyServices(ctx context.Context, serviceName string) ([]*ServiceInstance, error)
	CleanupExpiredServices(ctx context.Context, ttl time.Duration) error
}

// repository implements the Repository interface
type repository struct {
	db     *gorm.DB
	logger *logging.Logger
}

// NewRepository creates a new service registry repository with database connection
func NewRepository(db *gorm.DB, logger *logging.Logger) Repository {
	return &repository{
		db:     db,
		logger: logger,
	}
}

// RegisterService registers a new service instance
func (r *repository) RegisterService(ctx context.Context, instance *ServiceInstance) error {
	instance.RegisteredAt = time.Now()
	instance.LastHeartbeat = time.Now()

	if err := r.db.WithContext(ctx).Create(instance).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to register service")
		return fmt.Errorf("failed to register service: %w", err)
	}

	r.logger.WithFields(logging.Fields{
		"service_id":   instance.ID,
		"service_name": instance.ServiceName,
		"host":         instance.Host,
		"grpc_port":    instance.GRPCPort,
	}).Info("Service registered successfully")

	return nil
}

// UpdateHeartbeat updates the last heartbeat timestamp for a service
func (r *repository) UpdateHeartbeat(ctx context.Context, serviceID string) error {
	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("id = ? AND deleted_at IS NULL", serviceID).
		Update("last_heartbeat", time.Now())

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to update heartbeat")
		return fmt.Errorf("failed to update heartbeat: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	return nil
}

// DeregisterService marks a service as deregistered
func (r *repository) DeregisterService(ctx context.Context, serviceID string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("id = ? AND deleted_at IS NULL", serviceID).
		Updates(map[string]interface{}{
			"status":          ServiceStatusDeregistered,
			"deregistered_at": &now,
		})

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to deregister service")
		return fmt.Errorf("failed to deregister service: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	r.logger.WithFields(logging.Fields{
		"service_id": serviceID,
	}).Info("Service deregistered successfully")

	return nil
}

// GetHealthyServices retrieves healthy instances of a service by name
func (r *repository) GetHealthyServices(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := r.db.WithContext(ctx).
		Where("service_name = ? AND status = ? AND deleted_at IS NULL",
			serviceName, ServiceStatusHealthy).
		Order("last_heartbeat DESC").
		Find(&instances).Error; err != nil {
		return nil, fmt.Errorf("failed to get healthy services: %w", err)
	}

	return instances, nil
}

// CleanupExpiredServices removes services that haven't sent heartbeat within TTL
func (r *repository) CleanupExpiredServices(ctx context.Context, ttl time.Duration) error {
	expiredTime := time.Now().Add(-ttl)

	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("last_heartbeat < ? AND status != ? AND deleted_at IS NULL",
			expiredTime, ServiceStatusDeregistered).
		Updates(map[string]interface{}{
			"status":          ServiceStatusDeregistered,
			"deregistered_at": time.Now(),
		})

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to cleanup expired services")
		return fmt.Errorf("failed to cleanup expired services: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		r.logger.WithFields(logging.Fields{
			"expired_count": result.RowsAffected,
			"ttl":           ttl.String(),
		}).Info("Cleaned up expired services")
	}

	return nil
}
