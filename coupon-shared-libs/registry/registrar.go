package registry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// RegistrarConfig holds configuration for service registration
type RegistrarConfig struct {
	ServiceName string `mapstructure:"service_name"`
	Host        string `mapstructure:"host"`
	GRPCPort    int    `mapstructure:"grpc_port"`
}

// ServiceRegistrar handles service registration and lifecycle management in the registry
type ServiceRegistrar struct {
	config     *RegistrarConfig
	repository Repository
	logger     *logging.Logger

	serviceID string
	instance  *ServiceInstance

	// Lifecycle management
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	registered bool
	mu         sync.RWMutex
}

// NewServiceRegistrar creates a new service registrar with the provided configuration
func NewServiceRegistrar(cfg *RegistrarConfig, repo Repository, logger *logging.Logger) *ServiceRegistrar {
	ctx, cancel := context.WithCancel(context.Background())

	serviceID := fmt.Sprintf("%s-%s-%d",
		cfg.ServiceName, cfg.Host, cfg.GRPCPort)

	return &ServiceRegistrar{
		config:     cfg,
		repository: repo,
		logger:     logger,
		serviceID:  serviceID,
		ctx:        ctx,
		cancel:     cancel,
	}
}

// NewServiceRegistrarFromConfig creates a service registrar from shared config
func NewServiceRegistrarFromConfig(cfg *config.Config, repo Repository, logger *logging.Logger) *ServiceRegistrar {
	registrarConfig := &RegistrarConfig{
		ServiceName: cfg.Service.Name,
		Host:        cfg.GRPC.Host,
		GRPCPort:    cfg.Service.GRPCPort,
	}

	return NewServiceRegistrar(registrarConfig, repo, logger)
}

// Register registers the service instance
func (r *ServiceRegistrar) Register(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.registered {
		return fmt.Errorf("service already registered")
	}

	// Create service instance
	r.instance = &ServiceInstance{
		ID:          r.serviceID,
		ServiceName: r.config.ServiceName,
		Host:        r.config.Host,
		GRPCPort:    r.config.GRPCPort,
		Status:      ServiceStatusHealthy,
	}

	// Register with repository
	if err := r.repository.RegisterService(ctx, r.instance); err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}

	r.registered = true

	// Start heartbeat goroutine
	r.wg.Add(1)
	go r.heartbeatLoop()

	r.logger.WithFields(logging.Fields{
		"service_id":   r.serviceID,
		"service_name": r.config.ServiceName,
		"grpc_address": r.instance.GetGRPCAddress(),
	}).Info("Service registered successfully")

	return nil
}

// UpdateStatus updates the service status
func (r *ServiceRegistrar) UpdateStatus(ctx context.Context, status string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.registered {
		return fmt.Errorf("service not registered")
	}

	r.instance.Status = status

	// Update in database would require additional repository method
	// For now, this will be updated on next heartbeat

	r.logger.WithFields(logging.Fields{
		"service_id": r.serviceID,
		"status":     status,
	}).Info("Service status updated")

	return nil
}

// MarkHealthy marks the service as healthy
func (r *ServiceRegistrar) MarkHealthy(ctx context.Context) error {
	return r.UpdateStatus(ctx, ServiceStatusHealthy)
}

// MarkUnhealthy marks the service as unhealthy
func (r *ServiceRegistrar) MarkUnhealthy(ctx context.Context) error {
	return r.UpdateStatus(ctx, ServiceStatusUnhealthy)
}

// Deregister gracefully deregisters the service
func (r *ServiceRegistrar) Deregister(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.registered {
		return nil // Already deregistered
	}

	// Stop heartbeat loop
	r.cancel()
	r.wg.Wait()

	// Deregister from repository
	if err := r.repository.DeregisterService(ctx, r.serviceID); err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to deregister service")
		return fmt.Errorf("failed to deregister service: %w", err)
	}

	r.registered = false

	r.logger.WithFields(logging.Fields{
		"service_id": r.serviceID,
	}).Info("Service deregistered successfully")

	return nil
}

// GetServiceID returns the service ID
func (r *ServiceRegistrar) GetServiceID() string {
	return r.serviceID
}

// GetInstance returns the service instance
func (r *ServiceRegistrar) GetInstance() *ServiceInstance {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.instance
}

// IsRegistered returns true if the service is registered
func (r *ServiceRegistrar) IsRegistered() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.registered
}

// heartbeatLoop sends periodic heartbeats to maintain service registration
func (r *ServiceRegistrar) heartbeatLoop() {
	defer r.wg.Done()

	// Use sensible default: heartbeat every 30 seconds
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			if err := r.sendHeartbeat(); err != nil {
				r.logger.WithFields(logging.Fields{
					"error": err.Error(),
				}).Error("Failed to send heartbeat")
			}
		}
	}
}

// sendHeartbeat sends a heartbeat to the registry
func (r *ServiceRegistrar) sendHeartbeat() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := r.repository.UpdateHeartbeat(ctx, r.serviceID); err != nil {
		return fmt.Errorf("failed to update heartbeat: %w", err)
	}

	r.logger.WithFields(logging.Fields{
		"service_id": r.serviceID,
	}).Debug("Heartbeat sent successfully")

	return nil
}

// formatTags converts tags slice to string for storage
func (r *ServiceRegistrar) formatTags() string {
	// Return empty string since tags are not configurable anymore
	return ""
}

// formatMetadata converts metadata map to string for storage
func (r *ServiceRegistrar) formatMetadata() string {
	// Return empty string since metadata is not configurable anymore
	return ""
}
