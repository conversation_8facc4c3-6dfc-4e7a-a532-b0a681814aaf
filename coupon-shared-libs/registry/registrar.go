package registry

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// ServiceRegistrar handles service credential registration
type ServiceRegistrar struct {
	repository Repository
	logger     *logging.Logger
}

// RegistrationConfig contains the configuration for service registration
type RegistrationConfig struct {
	ServiceName    string
	ServiceVersion string
}

// RegistrationResult contains the result of service registration
type RegistrationResult struct {
	ServiceID string
	ClientID  string
	ClientKey string
}

// NewServiceRegistrar creates a new service registrar
func NewServiceRegistrar(db *database.DB, logger *logging.Logger) *ServiceRegistrar {
	return &ServiceRegistrar{
		repository: NewRepository(db.DB, logger),
		logger:     logger,
	}
}

// Register registers a service and returns the generated credentials
func (r *ServiceRegistrar) Register(ctx context.Context, config *RegistrationConfig) (*RegistrationResult, error) {
	if config.ServiceName == "" {
		return nil, fmt.Errorf("service name is required")
	}

	// Create new service credential
	credential, rawKey, err := NewServiceCredential(config.ServiceName, config.ServiceVersion)
	if err != nil {
		r.logger.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"service_name": config.ServiceName,
		}).Error("Failed to create service credential")
		return nil, fmt.Errorf("failed to create service credential: %w", err)
	}

	// Register in database
	if err := r.repository.RegisterService(ctx, credential); err != nil {
		r.logger.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"service_name": config.ServiceName,
			"client_id":    credential.ClientID,
		}).Error("Failed to register service credential in database")
		return nil, fmt.Errorf("failed to register service: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"service_id":   credential.ID,
		"service_name": credential.Name,
		"client_id":    credential.ClientID,
		"version":      credential.Version,
	}).Info("Service registered successfully")

	return &RegistrationResult{
		ServiceID: credential.ID,
		ClientID:  credential.ClientID,
		ClientKey: rawKey,
	}, nil
}

// ValidateCredentials validates service credentials
func (r *ServiceRegistrar) ValidateCredentials(ctx context.Context, clientID, clientKey string) (*ServiceCredential, error) {
	return r.repository.ValidateCredentials(ctx, clientID, clientKey)
}

// GetServiceByClientID retrieves service information by client ID
func (r *ServiceRegistrar) GetServiceByClientID(ctx context.Context, clientID string) (*ServiceCredential, error) {
	return r.repository.GetServiceByClientID(ctx, clientID)
}
