package registry

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// Manager manages service registration and discovery operations for a microservice
type Manager struct {
	config *config.Config
	logger *logging.Logger

	// Database connection for registry
	registryDB *database.DB
	repository Repository

	// Service registration
	registrar      *ServiceRegistrar
	healthReporter *HealthReporter

	// Service discovery cache
	serviceCache map[string][]*ServiceInstance
	cacheMutex   sync.RWMutex
	lastRefresh  map[string]time.Time

	// Lifecycle
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// State
	started bool
	mu      sync.RWMutex
}

// NewManager creates a new service registry manager with the provided configuration
func NewManager(cfg *config.Config, logger *logging.Logger) (*Manager, error) {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &Manager{
		config:       cfg,
		logger:       logger,
		ctx:          ctx,
		cancel:       cancel,
		serviceCache: make(map[string][]*ServiceInstance),
		lastRefresh:  make(map[string]time.Time),
	}

	// Initialize registry database if service registry or discovery is enabled
	if cfg.ServiceRegistry.Enabled || cfg.ServiceDiscovery.Enabled {
		if err := manager.initializeRegistryDatabase(); err != nil {
			cancel()
			return nil, fmt.Errorf("failed to initialize registry database: %w", err)
		}

		// Create repository
		manager.repository = NewRepository(manager.registryDB.DB, logger)
	}

	// Initialize service registration if enabled
	if cfg.ServiceRegistry.Enabled {
		// Create service registrar
		manager.registrar = NewServiceRegistrarFromConfig(cfg, manager.repository, logger)

		// Create health reporter
		manager.healthReporter = NewHealthReporter(manager.registrar, manager.repository, logger)
	}

	return manager, nil
}

// Start starts the service registry manager
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.started {
		return fmt.Errorf("manager already started")
	}

	// Start service registration if enabled
	if m.config.ServiceRegistry.Enabled && m.registrar != nil {
		if err := m.registrar.Register(ctx); err != nil {
			return fmt.Errorf("failed to register service: %w", err)
		}

		// Start health reporter
		if m.healthReporter != nil {
			m.healthReporter.Start()
		}

		// Start cleanup routine
		m.wg.Add(1)
		go m.cleanupLoop()
	}

	m.started = true

	m.logger.WithFields(logging.Fields{
		"registry_enabled":  m.config.ServiceRegistry.Enabled,
		"discovery_enabled": m.config.ServiceDiscovery.Enabled,
	}).Info("Service registry manager started")

	return nil
}

// Stop stops the service registry manager
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.started {
		return nil
	}

	// Stop health reporter
	if m.healthReporter != nil {
		m.healthReporter.Stop()
	}

	// Deregister service
	if m.registrar != nil {
		if err := m.registrar.Deregister(ctx); err != nil {
			m.logger.WithError(err).Error("Failed to deregister service")
		}
	}

	// Clear service cache
	m.cacheMutex.Lock()
	m.serviceCache = make(map[string][]*ServiceInstance)
	m.lastRefresh = make(map[string]time.Time)
	m.cacheMutex.Unlock()

	// Stop background routines
	m.cancel()
	m.wg.Wait()

	// Close registry database
	if m.registryDB != nil {
		if err := m.registryDB.Close(); err != nil {
			m.logger.WithError(err).Error("Failed to close registry database")
		}
	}

	m.started = false

	m.logger.Info("Service registry manager stopped")
	return nil
}

// GetRegistrar returns the service registrar
func (m *Manager) GetRegistrar() *ServiceRegistrar {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.registrar
}

// GetHealthReporter returns the health reporter
func (m *Manager) GetHealthReporter() *HealthReporter {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.healthReporter
}

// GetRepository returns the registry repository
func (m *Manager) GetRepository() Repository {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.repository
}

// AddHealthCheck adds a health check to the health reporter
func (m *Manager) AddHealthCheck(name string, checkFunc HealthCheckFunc) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.healthReporter == nil {
		return fmt.Errorf("health reporter not available")
	}

	m.healthReporter.AddHealthCheck(name, checkFunc)
	return nil
}

// DiscoverService discovers healthy service instances
func (m *Manager) DiscoverService(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	if m.repository == nil {
		return nil, fmt.Errorf("service discovery not enabled")
	}

	// Check cache first if discovery is enabled
	if m.config.ServiceDiscovery.Enabled {
		if instances := m.getCachedInstances(serviceName); instances != nil {
			return instances, nil
		}
	}

	// Fetch from repository
	instances, err := m.repository.GetHealthyServices(ctx, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to discover services: %w", err)
	}

	// Update cache if discovery is enabled
	if m.config.ServiceDiscovery.Enabled {
		m.updateCache(serviceName, instances)
	}

	return instances, nil
}

// GetServiceAddress gets a service address using simple round-robin
func (m *Manager) GetServiceAddress(ctx context.Context, serviceName string) (string, error) {
	instances, err := m.DiscoverService(ctx, serviceName)
	if err != nil {
		return "", err
	}

	if len(instances) == 0 {
		return "", fmt.Errorf("no healthy instances found for service: %s", serviceName)
	}

	// Simple random selection for load balancing
	instance := instances[rand.Intn(len(instances))]
	return instance.GetGRPCAddress(), nil
}

// initializeRegistryDatabase initializes the registry database connection
func (m *Manager) initializeRegistryDatabase() error {
	// Create database config for registry
	dbConfig := &config.DatabaseConfig{
		Host:         m.config.ServiceRegistry.DatabaseHost,
		Port:         m.config.ServiceRegistry.DatabasePort,
		User:         m.config.ServiceRegistry.DatabaseUser,
		Password:     m.config.ServiceRegistry.DatabasePassword,
		Name:         m.config.ServiceRegistry.DatabaseName,
		SSLMode:      "disable",
		MaxOpenConns: 10,
		MaxIdleConns: 5,
		MaxLifetime:  time.Hour,
	}

	// Create database connection
	db, err := database.NewPostgresDB(dbConfig, m.logger, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to registry database: %w", err)
	}

	m.registryDB = db

	// Run auto-migration for registry tables
	if err := m.autoMigrateRegistryTables(); err != nil {
		return fmt.Errorf("failed to migrate registry tables: %w", err)
	}

	return nil
}

// autoMigrateRegistryTables runs auto-migration for registry tables
func (m *Manager) autoMigrateRegistryTables() error {
	if err := m.registryDB.AutoMigrate(&ServiceInstance{}); err != nil {
		return fmt.Errorf("failed to migrate service_instances table: %w", err)
	}

	m.logger.Info("Registry database table migrated successfully")
	return nil
}

// cleanupLoop periodically cleans up expired services
func (m *Manager) cleanupLoop() {
	defer m.wg.Done()

	// Use sensible defaults: cleanup every 60 seconds, expire after 90 seconds
	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			if err := m.repository.CleanupExpiredServices(ctx, 90*time.Second); err != nil {
				m.logger.WithFields(logging.Fields{
					"error": err.Error(),
				}).Error("Failed to cleanup expired services")
			}
			cancel()
		}
	}
}

// IsRegistryEnabled returns true if service registry is enabled
func (m *Manager) IsRegistryEnabled() bool {
	return m.config.ServiceRegistry.Enabled
}

// IsDiscoveryEnabled returns true if service discovery is enabled
func (m *Manager) IsDiscoveryEnabled() bool {
	return m.config.ServiceDiscovery.Enabled
}

// getCachedInstances returns cached instances if they're still valid
func (m *Manager) getCachedInstances(serviceName string) []*ServiceInstance {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	instances, exists := m.serviceCache[serviceName]
	if !exists {
		return nil
	}

	lastRefresh, exists := m.lastRefresh[serviceName]
	// Use sensible default: cache expires after 60 seconds
	if !exists || time.Since(lastRefresh) > 60*time.Second {
		return nil
	}

	return instances
}

// updateCache updates the service cache
func (m *Manager) updateCache(serviceName string, instances []*ServiceInstance) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.serviceCache[serviceName] = instances
	m.lastRefresh[serviceName] = time.Now()
}
