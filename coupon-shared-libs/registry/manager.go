package registry

import (
"context"
"fmt"

"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// Manager manages service credential registration for a microservice
type Manager struct {
config     *config.Config
logger     *logging.Logger
registrar  *ServiceRegistrar
registryDB *database.DB
}

// NewManager creates a new service registry manager
func NewManager(cfg *config.Config, logger *logging.Logger) (*Manager, error) {
manager := &Manager{
config: cfg,
logger: logger,
}

// Initialize database connection for registry if enabled
if cfg.ServiceRegistry.Enabled {
dbConfig := &config.DatabaseConfig{
Host:         cfg.ServiceRegistry.DatabaseHost,
Port:         cfg.ServiceRegistry.DatabasePort,
User:         cfg.ServiceRegistry.DatabaseUser,
Password:     cfg.ServiceRegistry.DatabasePassword,
Name:         cfg.ServiceRegistry.DatabaseName,
SSLMode:      "disable",
MaxOpenConns: 10,
MaxIdleConns: 5,
MaxLifetime:  "1h",
}

db, err := database.NewDB(dbConfig, logger)
if err != nil {
return nil, fmt.Errorf("failed to connect to registry database: %w", err)
}

manager.registryDB = db

// Auto-migrate service credentials table
if err := db.DB.AutoMigrate(&ServiceCredential{}); err != nil {
return nil, fmt.Errorf("failed to migrate service credentials table: %w", err)
}

// Create service registrar
manager.registrar = NewServiceRegistrar(db, logger)
}

return manager, nil
}

// Start initializes the service registry manager
func (m *Manager) Start(ctx context.Context) error {
if !m.config.ServiceRegistry.Enabled {
m.logger.Info("Service registry is disabled")
return nil
}

m.logger.Info("Service registry manager started")
return nil
}

// Stop gracefully shuts down the service registry manager
func (m *Manager) Stop(ctx context.Context) error {
if m.registryDB != nil {
if err := m.registryDB.Close(); err != nil {
m.logger.WithError(err).Error("Failed to close registry database connection")
}
}

m.logger.Info("Service registry manager stopped")
return nil
}

// RegisterService registers a new service and returns credentials
func (m *Manager) RegisterService(ctx context.Context, serviceName, version string) (*RegistrationResult, error) {
if !m.config.ServiceRegistry.Enabled || m.registrar == nil {
return nil, fmt.Errorf("service registry is not enabled")
}

config := &RegistrationConfig{
ServiceName:    serviceName,
ServiceVersion: version,
}

return m.registrar.Register(ctx, config)
}

// ValidateCredentials validates service credentials
func (m *Manager) ValidateCredentials(ctx context.Context, clientID, clientKey string) (*ServiceCredential, error) {
if !m.config.ServiceRegistry.Enabled || m.registrar == nil {
return nil, fmt.Errorf("service registry is not enabled")
}

return m.registrar.ValidateCredentials(ctx, clientID, clientKey)
}

// GetServiceByClientID retrieves service information by client ID
func (m *Manager) GetServiceByClientID(ctx context.Context, clientID string) (*ServiceCredential, error) {
if !m.config.ServiceRegistry.Enabled || m.registrar == nil {
return nil, fmt.Errorf("service registry is not enabled")
}

return m.registrar.GetServiceByClientID(ctx, clientID)
}

// IsRegistryEnabled returns true if service registry is enabled
func (m *Manager) IsRegistryEnabled() bool {
return m.config.ServiceRegistry.Enabled
}
