package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	userv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/registry"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-user-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/service"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("config error: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, "json")
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("tracer error: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	// --- Infrastructure ---
	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("db error: %v", err)
	}

	// Run GORM AutoMigrate
	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []interface{}{
		&model.User{},
	}

	if err := autoMigrator.AutoMigrate(models...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}
	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)
	kafkaProducer := kafka.NewProducer(&cfg.Kafka, logger)
	authClient, err := clients.NewAuthClient(cfg.DownstreamServices.AuthServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("auth client error: %v", err)
	}

	// --- DI ---
	repo := repository.NewUserRepository(db, redisClient, logger)
	svc := service.NewUserService(repo, authClient, kafkaProducer, logger, &cfg.Kafka.Topics)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	// Initialize service registry manager
	registryManager, err := registry.NewManager(cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to create registry manager: %v", err)
	}

	// Add health checks to registry
	if registryManager.IsRegistryEnabled() {
		registryManager.AddHealthCheck("database", registry.CreateDatabaseHealthCheck(db.Health))
		registryManager.AddHealthCheck("redis", registry.CreateRedisHealthCheck(redisClient.Health))
	}

	// --- Run ---
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// Start service registry
	if err := registryManager.Start(ctx); err != nil {
		logger.Fatalf("Failed to start registry manager: %v", err)
	}

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		selectiveAuthFunc := middleware.CreateSelectiveAuthFunc(jwtManager)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authClient, selectiveAuthFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, healthChecker, appMetrics, logger)
	}()

	wg.Wait()

	// Stop service registry
	if err := registryManager.Stop(ctx); err != nil {
		logger.Errorf("Failed to stop registry manager: %v", err)
	}

	tracer.Close()
	db.Close()
	redisClient.Close()
	kafkaProducer.Close()
	authClient.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.UserService, authClient *clients.AuthClient, authFunc func(context.Context) (context.Context, error)) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
	grpcHandler := grpc_handler.NewUserServer(svc, authClient)
	userv1.RegisterUserServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

// startHTTPServer remains unchanged.
func startHTTPServer(ctx context.Context, cfg *config.Config, healthChecker *health.HealthChecker, metrics *metrics.Metrics, logger *logging.Logger) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
